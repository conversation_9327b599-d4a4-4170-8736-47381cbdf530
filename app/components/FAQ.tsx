"use client"

import { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

const faqItems = [
  "How is this different from other influence or persuasion programs?",
  "How soon can I expect to see results?",
  "Is this manipulative or unethical?",
  "Do I need any background in psychology to use this?",
  "I'm not naturally charismatic. Will this still work for me?"
];

const FAQ = () => {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  return (
    <div className="py-16">
      <div className="max-w-4xl mx-auto px-4">
        <h2 className="text-5xl font-bold mb-12">FAQ</h2>
        
        <div className="space-y-4">
          {faqItems.map((question, index) => (
            <div key={index} className="border border-gray-200 rounded-lg">
              <button
                className="w-full p-6 text-left flex items-center justify-between hover:bg-gray-50"
                onClick={() => toggleSection(`faq-${index}`)}
              >
                <span className="font-semibold text-lg">{question}</span>
                {expandedSection === `faq-${index}` ? (
                  <ChevronUp className="w-6 h-6" />
                ) : (
                  <ChevronDown className="w-6 h-6" />
                )}
              </button>
              {expandedSection === `faq-${index}` && (
                <div className="px-6 pb-6 border-t">
                  <p className="text-gray-600 pt-4">
                    Answer to "{question}" would go here...
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default FAQ; 