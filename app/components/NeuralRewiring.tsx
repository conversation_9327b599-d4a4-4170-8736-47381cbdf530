const NeuralRewiring = () => {
  return (
    <div className="bg-gray-50 py-16">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-5xl font-bold mb-4">
            includes all the secrets to persuade and influence anyone, anytime.
          </h2>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="bg-gray-200 rounded-lg p-8 aspect-square flex items-center justify-center">
            <div className="text-center">
              <div className="w-32 h-32 mx-auto mb-4 opacity-50">
                <svg viewBox="0 0 200 200" className="w-full h-full">
                  <path d="M100,20 Q180,100 100,180 Q20,100 100,20" fill="none" stroke="currentColor" strokeWidth="2"/>
                  <circle cx="60" cy="80" r="25" fill="none" stroke="currentColor" strokeWidth="2"/>
                  <circle cx="140"cy="80" r="25" fill="none" stroke="currentColor" strokeWidth="2"/>
                  <path d="M60,80 L100,120 L140,80" fill="none" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
            </div>
          </div>

          <div>
            <div className="text-6xl font-bold text-gray-300 mb-4">#1</div>
            <h3 className="text-3xl font-bold mb-6">The Neural Rewiring</h3>
            <p className="text-lg text-gray-600 mb-6">
              21 days. 21 exercises. Rewire your brain into a weapon of mass influence. 10 minutes a day is all it takes to transcend human limitations and see the code behind every interaction.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NeuralRewiring; 