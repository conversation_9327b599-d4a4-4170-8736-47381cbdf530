import { Star } from "lucide-react";

interface TestimonialImage {
  src: string;
  alt: string;
}

const testimonialImages: TestimonialImage[] = [
  { src: "/api/placeholder/120/160", alt: "Testimonial 1" },
  { src: "/api/placeholder/120/160", alt: "Testimonial 2" },
  { src: "/api/placeholder/120/160", alt: "Testimonial 3" },
  { src: "/api/placeholder/120/160", alt: "Testimonial 4" },
  { src: "/api/placeholder/120/160", alt: "Testimonial 5" },
  { src: "/api/placeholder/120/160", alt: "Testimonial 6" }
];

const Testimonials = () => {
  return (
    <div className="py-16">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-5xl font-bold mb-8">
            Readers have found immense power with The Puppet Master's Bible™
          </h2>
          
          <div className="flex items-center justify-center gap-8 mb-8">
            <div className="text-center">
              <div className="flex items-center gap-2">
                <span className="text-4xl font-bold">4.9</span>
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-6 h-6 fill-current" />
                  ))}
                </div>
              </div>
              <p className="text-gray-600">Based on 172 reviews</p>
            </div>
            
            <div className="text-center">
              <div className="text-4xl font-bold">99%</div>
              <p className="text-gray-600">would recommend these products</p>
            </div>
          </div>

          {/* Rating Breakdown */}
          <div className="max-w-md mx-auto mb-8">
            {[5, 4, 3, 2, 1].map((stars) => (
              <div key={stars} className="flex items-center gap-2 mb-1">
                <span className="w-4 text-sm">{stars}</span>
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-yellow-400 h-2 rounded-full"
                    style={{ width: stars === 5 ? '92%' : stars === 4 ? '6%' : '1%' }}
                  ></div>
                </div>
                <span className="w-8 text-sm text-right">
                  {stars === 5 ? '159' : stars === 4 ? '11' : '1'}
                </span>
              </div>
            ))}
          </div>

          {/* Testimonial Images */}
          <div className="grid grid-cols-6 gap-4 mb-8">
            {testimonialImages.map((img, index) => (
              <div key={index} className="bg-gray-200 rounded-lg aspect-[3/4]"></div>
            ))}
          </div>

          {/* Reviews */}
          <div className="text-left max-w-2xl mx-auto">
            <div className="flex items-center gap-4 mb-4">
              <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              <div>
                <div className="font-semibold">George M.</div>
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-current" />
                  ))}
                </div>
              </div>
            </div>
            <p className="text-gray-600 mb-4">
              This book completely changed my approach to business and relationships. The frameworks are practical and powerful.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Testimonials; 