"use client"

import { useState } from 'react';

type EditionType = 'digital' | 'audiobook' | 'hardcover';

interface Edition {
  id: EditionType;
  name: string;
  description: string;
  price: string;
  originalPrice: string;
  badge?: string;
}

const editions: Edition[] = [
  {
    id: 'digital',
    name: 'Digital',
    description: 'Access to Ebook & PDF',
    price: '₹ 6,000',
    originalPrice: '₹ 22,100',
  },
  {
    id: 'audiobook',
    name: 'Audiobook',
    description: '12hrs of Immersive Audio',
    price: '₹ 7,700',
    originalPrice: '₹ 22,100',
    badge: '+ FREE Digital'
  },
  {
    id: 'hardcover',
    name: 'Hardcover',
    description: 'Limited Edition Hardcover',
    price: '₹ 8,600',
    originalPrice: '₹ 22,100',
    badge: '+ FREE Digital & Audio'
  }
];

const EditionSelector = () => {
  const [selectedEdition, setSelectedEdition] = useState<EditionType>('digital');

  const handleEditionSelect = (edition: EditionType) => {
    setSelectedEdition(edition);
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <span className="font-semibold">Edition: Digital</span>
        <span className="text-sm text-blue-600 underline">Compare Editions</span>
      </div>
      
      <div className="space-y-3">
        {editions.map((edition) => (
          <div
            key={edition.id}
            className={`p-4 border rounded-lg cursor-pointer flex items-center justify-between ${
              selectedEdition === edition.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
            }`}
            onClick={() => handleEditionSelect(edition.id as EditionType)}
          >
            <div className="flex items-center gap-3">
              <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                selectedEdition === edition.id ? 'border-blue-500' : 'border-gray-300'
              }`}>
                {selectedEdition === edition.id && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                )}
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-semibold">{edition.name}</span>
                  {edition.badge && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      {edition.badge}
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600">{edition.description}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="font-bold text-red-600">{edition.price}</div>
              <div className="text-sm text-gray-500 line-through">{edition.originalPrice}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default EditionSelector; 