"use client"

import { Globe, Menu, ShoppingCart } from "lucide-react";

const Header = () => {
  return (
    <header className="flex items-center justify-between p-4 border-b border-gray-800">
      <Menu className="w-6 h-6" />
      <h1 className="text-2xl font-bold tracking-wider">TOM WALKER</h1>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Globe className="w-4 h-4" />
          <span className="text-sm">INR ₹</span>
        </div>
        <ShoppingCart className="w-6 h-6" />
      </div>
    </header>
  );
};

export default Header; 