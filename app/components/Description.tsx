"use client"

import { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

const Description = () => {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  return (
    <div className="bg-black py-16">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-5xl font-bold mb-6 leading-tight">
            The rules of success are a lie. They were designed to keep you struggling while others thrive.{' '}
            <span className="text-[#925cff]">
              The Puppet Master's Bible tears down the fake playbook and gives you the real formula for control.
            </span>{' '}
            Learn how to create opportunities, dominate your environment, and silence the competition. You don't need permission to succeed. Rewrite the rules and take what's yours.
          </h2>
        </div>

        {/* Expandable Sections */}
        <div className="space-y-4">
          {[
            'Why It Works',
            'Who It\'s For',
            'The Ethics',
            'What\'s Included',
            'Table of Contents',
            'Digital Edition',
            'Money-Back Guarantee'
          ].map((section) => (
            <div key={section} className="border border-gray-200 rounded-lg">
              <button
                className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50"
                onClick={() => toggleSection(section)}
              >
                <span className="font-semibold">{section}</span>
                {expandedSection === section ? (
                  <ChevronUp className="w-5 h-5" />
                ) : (
                  <ChevronDown className="w-5 h-5" />
                )}
              </button>
              {expandedSection === section && (
                <div className="p-4 pt-0 border-t">
                  <p className="text-gray-600">Content for {section} would go here...</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Description; 