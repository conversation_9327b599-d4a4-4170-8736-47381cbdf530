const BookPreview = () => {
  return (
    <div className="relative">
      <div className="bg-gradient-to-b from-white to-[#925cff] rounded-2xl p-8 text-black relative overflow-hidden">
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 flex gap-2">
          <div className="w-3 h-3 bg-gray-600 rounded-full"></div>
          <div className="w-3 h-3 bg-gray-600 rounded-full"></div>
          <div className="w-3 h-3 bg-gray-600 rounded-full"></div>
          <div className="w-3 h-3 bg-gray-600 rounded-full"></div>
          <div className="w-3 h-3 bg-gray-600 rounded-full"></div>
        </div>
        
        <div className="mt-16 text-center">
          <h2 className="text-4xl font-bold mb-4 font-serif">The Puppet Master's Bible</h2>
          <p className="text-lg mb-8">Pull the hidden strings of the mind to win hearts and open wallets.</p>
          <p className="text-xl font-bold">TOM WALKER</p>
        </div>
        
        <div className="absolute top-0 left-0 w-full h-full opacity-20">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-gray-400 rounded-full opacity-30"></div>
          <div className="absolute top-1/3 right-1/4 w-24 h-24 bg-gray-400 rounded-full opacity-20"></div>
        </div>
      </div>
    </div>
  );
};

export default BookPreview; 