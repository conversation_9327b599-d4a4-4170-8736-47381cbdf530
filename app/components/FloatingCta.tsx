const FloatingCta = () => {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-black border-t border-gray-800 p-4 lg:hidden">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-b from-white to-[#925cff] rounded"></div>
          <div>
            <div className="font-semibold">The Puppet Master's Bible™</div>
            <div className="text-sm text-gray-400">Digital</div>
            <div className="flex items-center gap-2">
              <span className="font-bold text-[#925cff]">Rs. 6,000.00</span>
              <span className="text-sm text-gray-500 line-through">Rs. 22,100.00</span>
            </div>
          </div>
        </div>
        <button className="bg-gradient-to-r from-white to-[#925cff] text-black px-6 py-2 rounded-lg font-semibold hover:opacity-90 transition-colors">
          Buy Now
        </button>
      </div>
    </div>
  )
}

export default FloatingCta; 