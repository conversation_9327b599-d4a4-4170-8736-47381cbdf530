import { GoogleSpreadsheet } from 'google-spreadsheet';
import { JWT } from 'google-auth-library';

// Initialize Google Sheets authentication
export function createGoogleSheetsAuth() {
  return new JWT({
    email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
    key: process.env.GOOGLE_PRIVATE_KEY!.replace(/\\n/g, '\n'),
    scopes: ['https://www.googleapis.com/auth/spreadsheets'],
  });
}

// Get Google Spreadsheet instance
export function getGoogleSpreadsheet() {
  const auth = createGoogleSheetsAuth();
  return new GoogleSpreadsheet(process.env.GOOGLE_SHEET_ID!, auth);
}

// Types for form data
export interface FormSubmissionData {
  name: string;
  email: string;
  uniqueID?: string;
  status?: string;
  orderID?: string;
  paymentID?: string;
  timestamp?: string;
}

// Add a new form submission to Google Sheets
export async function addFormSubmission(data: FormSubmissionData) {
  const doc = getGoogleSpreadsheet();
  await doc.loadInfo();
  const sheet = doc.sheetsByIndex[0];

  const rowData = {
    Timestamp: data.timestamp || new Date().toLocaleString(),
    Name: data.name,
    Email: data.email,
    UniqueID: data.uniqueID,
    Status: data.status || 'Pending Payment',
    OrderID: data.orderID || '',
    PaymentID: data.paymentID || '',
  };

  await sheet.addRow(rowData);
  return rowData;
}

// Update payment status in Google Sheets
export async function updatePaymentStatus(
  uniqueID: string, 
  orderID: string, 
  paymentID: string
) {
  const doc = getGoogleSpreadsheet();
  await doc.loadInfo();
  const sheet = doc.sheetsByIndex[0];

  const rows = await sheet.getRows();
  const targetRow = rows.find(row => row.get('UniqueID') === uniqueID);
  
  if (!targetRow) {
    throw new Error('Record not found with provided UniqueID');
  }

  targetRow.set('Status', 'Payment Complete');
  targetRow.set('OrderID', orderID);
  targetRow.set('PaymentID', paymentID);
  targetRow.set('Timestamp', new Date().toLocaleString());
  
  await targetRow.save();
  
  return {
    uniqueID,
    status: 'Payment Complete',
    orderID,
    paymentID,
    timestamp: new Date().toLocaleString()
  };
}

// Find and update payment status by email (for webhook usage)
export async function updatePaymentStatusByEmail(
  email: string, 
  orderID: string, 
  paymentID: string
) {
  const doc = getGoogleSpreadsheet();
  await doc.loadInfo();
  const sheet = doc.sheetsByIndex[0];

  const rows = await sheet.getRows();
  const targetRow = rows.find(row => 
    row.get('Email') === email && 
    row.get('Status') === 'Pending Payment'
  );
  
  if (!targetRow) {
    throw new Error(`No pending payment record found for email: ${email}`);
  }

  targetRow.set('Status', 'Payment Complete');
  targetRow.set('OrderID', orderID);
  targetRow.set('PaymentID', paymentID);
  targetRow.set('Timestamp', new Date().toLocaleString());
  
  await targetRow.save();
  
  return {
    email,
    uniqueID: targetRow.get('UniqueID'),
    status: 'Payment Complete',
    orderID,
    paymentID,
    timestamp: new Date().toLocaleString()
  };
}
