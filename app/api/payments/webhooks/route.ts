import { NextRequest, NextResponse } from 'next/server';
import { sendEbook } from '@/app/lib/email';
import { updatePaymentStatusByEmail } from '@/app/lib/google-sheets';

export async function POST(request: NextRequest) {
  console.log('Webhook received a request.');
  try {
    const webhookData = await request.json();
    console.log('Webhook data parsed successfully:', webhookData);
    const { order, payment, customer_details } = webhookData.data;

    if (payment && payment.payment_status === 'SUCCESS') {
      console.log('Payment status is SUCCESS.');
      const customerEmail = customer_details.customer_email;
      const customerName = customer_details.customer_name || 'Valued Customer';
      const orderId = order.order_id;
      const paymentId = payment.cf_payment_id;

      console.log(`Payment for order ${orderId} was successful.`);

      // Update Google Sheets with payment completion
      try {
        await updatePaymentStatusByEmail(customerEmail, orderId, paymentId);
        console.log(`Google Sheets updated for order ${orderId}`);
      } catch (sheetError) {
        console.error(`Failed to update Google Sheets for order ${orderId}:`, sheetError);
        // Continue with email sending even if sheets update fails
      }

      console.log(`Attempting to send e-book to ${customerEmail}...`);

      const emailResult = await sendEbook(customerEmail, customerName);

      if (emailResult.success) {
        console.log(`E-book for order ${orderId} sent successfully.`);
      } else {
        console.error(`Failed to send e-book for order ${orderId}:`, emailResult.error);
      }
    } else {
      console.log(`Webhook received but payment status was not SUCCESS. Status: ${payment?.payment_status}`);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('CRITICAL: Webhook processing failed with an error:', error);
    return NextResponse.json({ success: false, error: 'Webhook processing failed' }, { status: 400 });
  }
}