import { NextRequest, NextResponse } from 'next/server';
import { updatePaymentStatus } from '@/app/lib/google-sheets';

export async function POST(request: NextRequest) {
  try {
    const { uniqueID, orderID, paymentID } = await request.json();

    if (!uniqueID) {
      return NextResponse.json({ error: 'UniqueID is required' }, { status: 400 });
    }

    // Update payment status in Google Sheets
    const updatedData = await updatePaymentStatus(uniqueID, orderID || '', paymentID || '');

    return NextResponse.json({
      success: true,
      message: 'Payment status updated successfully',
      updatedData
    });

  } catch (error) {
    console.error('Error updating payment status in Google Sheets:', error);

    if (error instanceof Error && error.message.includes('Record not found')) {
      return NextResponse.json({ error: error.message }, { status: 404 });
    }

    return NextResponse.json({
      error: 'Failed to update payment status'
    }, { status: 500 });
  }
}
