import { NextRequest, NextResponse } from 'next/server';
import { addFormSubmission } from '@/app/lib/google-sheets';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const { name, email } = await request.json();

    if (!name || !email) {
      return NextResponse.json({ error: 'Name and email are required' }, { status: 400 });
    }

    const uniqueID = uuidv4(); // Generate a unique ID for this transaction

    // Add form submission to Google Sheets
    const rowData = await addFormSubmission({
      name,
      email,
      uniqueID,
      status: 'Pending Payment'
    });

    return NextResponse.json({
      success: true,
      uniqueID: uniqueID,
      message: 'Form submitted successfully',
      data: rowData
    });

  } catch (error) {
    console.error('Error submitting form to Google Sheets:', error);
    return NextResponse.json({
      error: 'Failed to submit form'
    }, { status: 500 });
  }
}
