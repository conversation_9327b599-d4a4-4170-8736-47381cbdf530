# Google Sheets Integration API Usage

This document explains how to use the Google Sheets integration APIs for form submission and payment tracking.

## Overview

The system provides three main API endpoints:

1. **Form Submission API** (`/api/submit-form`) - Creates initial entry in Google Sheets
2. **Payment Completion API** (`/api/payment-complete`) - Updates payment status manually
3. **Webhook Integration** - Automatically updates payment status when payment is completed

## Google Sheets Structure

Your Google Sheet should have the following columns:
- `Timestamp` - When the form was submitted/payment completed
- `Name` - Customer name
- `Email` - Customer email
- `UniqueID` - Unique identifier for tracking
- `Status` - Payment status ("Pending Payment" or "Payment Complete")
- `OrderID` - Order ID from payment gateway
- `PaymentID` - Payment ID from payment gateway

## API Endpoints

### 1. Form Submission API

**Endpoint:** `POST /api/submit-form`

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "uniqueID": "550e8400-e29b-41d4-a716-446655440000",
  "message": "Form submitted successfully",
  "data": {
    "Timestamp": "1/15/2025, 10:30:00 AM",
    "Name": "John Doe",
    "Email": "<EMAIL>",
    "UniqueID": "550e8400-e29b-41d4-a716-446655440000",
    "Status": "Pending Payment",
    "OrderID": "",
    "PaymentID": ""
  }
}
```

### 2. Payment Completion API

**Endpoint:** `POST /api/payment-complete`

**Request Body:**
```json
{
  "uniqueID": "550e8400-e29b-41d4-a716-446655440000",
  "orderID": "ebook-order-1642234200000-abc123",
  "paymentID": "cf_payment_123456789"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment status updated successfully",
  "updatedData": {
    "uniqueID": "550e8400-e29b-41d4-a716-446655440000",
    "status": "Payment Complete",
    "orderID": "ebook-order-1642234200000-abc123",
    "paymentID": "cf_payment_123456789",
    "timestamp": "1/15/2025, 11:00:00 AM"
  }
}
```

## Usage Flow

### Complete Integration Flow:

1. **Form Submission:**
   ```javascript
   // When user submits the form
   const response = await fetch('/api/submit-form', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({
       name: 'John Doe',
       email: '<EMAIL>'
     })
   });
   
   const result = await response.json();
   const uniqueID = result.uniqueID; // Store this for later use
   ```

2. **Payment Processing:**
   - User proceeds to payment
   - Payment is processed through your existing payment gateway
   - Webhook automatically updates Google Sheets when payment succeeds

3. **Manual Payment Update (if needed):**
   ```javascript
   // If you need to manually update payment status
   const response = await fetch('/api/payment-complete', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({
       uniqueID: 'stored-unique-id',
       orderID: 'order-id-from-payment',
       paymentID: 'payment-id-from-gateway'
     })
   });
   ```

## Webhook Integration

The payment webhook (`/api/payments/webhooks`) automatically:
- Receives payment completion notifications
- Updates Google Sheets with payment status
- Sends the e-book to the customer

The webhook looks for pending payments by email address and updates them to "Payment Complete" status.

## Environment Variables Required

Make sure these environment variables are set:
- `GOOGLE_SHEET_ID` - Your Google Sheet ID
- `GOOGLE_SERVICE_ACCOUNT_EMAIL` - Service account email
- `GOOGLE_PRIVATE_KEY` - Service account private key

## Error Handling

All APIs include proper error handling:
- 400 for missing required fields
- 404 for records not found
- 500 for server errors

The webhook continues to send emails even if Google Sheets update fails, ensuring customers still receive their e-books.
